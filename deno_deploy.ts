// 抖音视频下载服务 - <PERSON><PERSON>ploy 版本
const pattern = /"video":{"play_addr":{"uri":"([a-z0-9]+)"/;
const cVUrl = "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=1080p&line=0";
const statsRegex = /"statistics"\s*:\s*\{([\s\S]*?)\},/;
const regex = /"nickname":\s*"([^"]+)",\s*"signature":\s*"([^"]+)"/;
const ctRegex = /"create_time":\s*(\d+)/;
const descRegex = /"desc":\s*"([^"]+)"/;

interface DouyinVideoInfo {
  aweme_id: string | null;
  comment_count: number | null;
  digg_count: number | null;
  share_count: number | null;
  collect_count: number | null;
  nickname: string | null;
  signature: string | null;
  desc: string | null;
  create_time: string | null;
  video_url: string | null;
  type: string | null;
  image_url_list: string[] | null;
}

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

async function doGet(url: string): Promise<Response> {
  const headers = new Headers();
  headers.set(
    "User-Agent",
    "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36",
  );
  const resp = await fetch(url, { method: "GET", headers });
  return resp;
}

async function getVideoInfo(url: string): Promise<DouyinVideoInfo> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  
  const video_url = cVUrl.replace("%s", match[1]);
  const auMatch = body.match(regex);
  const ctMatch = body.match(ctRegex);
  const descMatch = body.match(descRegex);
  const statsMatch = body.match(statsRegex);
  
  if (statsMatch) {
    const innerContent = statsMatch[0];
    const awemeIdMatch = innerContent.match(/"aweme_id"\s*:\s*"([^"]+)"/);
    const commentCountMatch = innerContent.match(/"comment_count"\s*:\s*(\d+)/);
    const diggCountMatch = innerContent.match(/"digg_count"\s*:\s*(\d+)/);
    const shareCountMatch = innerContent.match(/"share_count"\s*:\s*(\d+)/);
    const collectCountMatch = innerContent.match(/"collect_count"\s*:\s*(\d+)/);
    
    const douyinVideoInfo: DouyinVideoInfo = {
      aweme_id: awemeIdMatch ? awemeIdMatch[1] : null,
      comment_count: commentCountMatch ? parseInt(commentCountMatch[1]) : null,
      digg_count: diggCountMatch ? parseInt(diggCountMatch[1]) : null,
      share_count: shareCountMatch ? parseInt(shareCountMatch[1]) : null,
      collect_count: collectCountMatch ? parseInt(collectCountMatch[1]) : null,
      nickname: null,
      signature: null,
      desc: null,
      create_time: null,
      video_url: video_url,
      type: "video",
      image_url_list: null
    };
    
    if (auMatch) {
      douyinVideoInfo.nickname = auMatch[1];
      douyinVideoInfo.signature = auMatch[2];
    }
    if (ctMatch) {
      const date = new Date(parseInt(ctMatch[1]) * 1000);
      douyinVideoInfo.create_time = formatDate(date);
    }
    if (descMatch) {
      douyinVideoInfo.desc = descMatch[1];
    }
    
    return douyinVideoInfo;
  } else {
    throw new Error("No stats found in the response.");
  }
}

async function getVideoUrl(url: string): Promise<string> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  return cVUrl.replace("%s", match[1]);
}

// 生成现代化的HTML页面
function generateHTML(): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频无水印下载工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 800px; margin: 0 auto; background: white;
            border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white; padding: 40px 30px; text-align: center;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .main-content { padding: 40px 30px; }
        .input-section { margin-bottom: 30px; }
        .input-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
        .url-input {
            width: 100%; padding: 15px; border: 2px solid #e1e8ed;
            border-radius: 10px; font-size: 16px; transition: all 0.3s ease;
        }
        .url-input:focus {
            outline: none; border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .button-group { display: flex; gap: 15px; flex-wrap: wrap; }
        .btn {
            flex: 1; min-width: 150px; padding: 15px 25px; border: none;
            border-radius: 10px; font-size: 16px; font-weight: 600;
            cursor: pointer; transition: all 0.3s ease;
        }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-secondary { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0,0,0,0.1); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .loading { display: none; text-align: center; margin: 20px 0; }
        .spinner {
            border: 3px solid #f3f3f3; border-top: 3px solid #667eea;
            border-radius: 50%; width: 40px; height: 40px;
            animation: spin 1s linear infinite; margin: 0 auto 10px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .result-section { margin-top: 30px; display: none; }
        .result-card {
            background: #f8f9fa; border-radius: 15px; padding: 25px;
            border-left: 5px solid #667eea;
        }
        .result-title { font-size: 1.2rem; font-weight: 600; margin-bottom: 15px; color: #333; }
        .error-message {
            background: #f8d7da; color: #721c24; padding: 15px;
            border-radius: 10px; margin-top: 20px; border-left: 5px solid #dc3545;
        }
        .success-message {
            background: #d4edda; color: #155724; padding: 15px;
            border-radius: 10px; margin-top: 20px; border-left: 5px solid #28a745;
        }
        .copy-btn {
            background: #28a745; color: white; border: none; padding: 8px 15px;
            border-radius: 5px; cursor: pointer; font-size: 12px; margin-left: 10px;
        }
        .copy-btn:hover { background: #218838; }
        .usage-guide {
            background: #e3f2fd; padding: 20px; border-radius: 10px; margin-top: 30px;
        }
        .usage-guide h3 { color: #1976d2; margin-bottom: 15px; }
        .usage-guide ul { list-style: none; padding-left: 0; }
        .usage-guide li {
            margin-bottom: 10px; padding-left: 20px; position: relative;
        }
        .usage-guide li:before {
            content: "✓"; position: absolute; left: 0; color: #1976d2; font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 抖音视频下载工具</h1>
            <p>快速获取无水印视频链接和详细信息</p>
        </div>
        <div class="main-content">
            <div class="input-section">
                <div class="input-group">
                    <label for="videoUrl">请输入抖音视频分享链接：</label>
                    <input type="text" id="videoUrl" class="url-input" 
                           placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本">
                </div>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="getVideoUrl()">📥 获取视频链接</button>
                    <button class="btn btn-secondary" onclick="getVideoInfo()">📊 获取详细信息</button>
                </div>
            </div>
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在解析视频信息，请稍候...</p>
            </div>
            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <div class="result-title" id="resultTitle">解析结果</div>
                    <div id="resultContent"></div>
                </div>
            </div>
            <div class="usage-guide">
                <h3>📖 使用说明</h3>
                <ul>
                    <li>支持抖音APP分享的短链接（如：https://v.douyin.com/xxxx/）</li>
                    <li>可以直接粘贴抖音分享的完整文本，系统会自动提取链接</li>
                    <li>"获取视频链接"返回可直接下载的无水印视频地址</li>
                    <li>"获取详细信息"返回视频的完整元数据信息</li>
                    <li>点击复制按钮可快速复制结果到剪贴板</li>
                </ul>
            </div>
        </div>
    </div>
    <script>
        console.log('JavaScript 已加载');
        const urlRegex = /https?:\\/\\/v\\.douyin\\.com\\/[A-Za-z0-9]+\\/?/;
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }
        
        function showResult(title, content) {
            document.getElementById('resultTitle').textContent = title;
            document.getElementById('resultContent').innerHTML = content;
            document.getElementById('resultSection').style.display = 'block';
        }
        
        function showError(message) {
            showResult('解析失败', '<div class="error-message">❌ ' + message + '</div>');
        }
        
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => alert('已复制到剪贴板！'));
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已复制到剪贴板！');
            }
        }
        
        function extractAndValidateUrl() {
            const input = document.getElementById('videoUrl').value.trim();
            if (!input) {
                showError('请输入抖音视频链接');
                return null;
            }
            const match = input.match(urlRegex);
            if (match) return match[0];
            if (input.startsWith('http')) return input;
            showError('未找到有效的抖音视频链接，请检查输入格式');
            return null;
        }
        
        function getVideoUrl() {
            console.log('getVideoUrl 被调用');
            const url = extractAndValidateUrl();
            if (!url) return;
            showLoading();
            fetch('?url=' + encodeURIComponent(url))
                .then(response => response.text())
                .then(result => {
                    hideLoading();
                    const content = '<div class="success-message">✅ 视频链接获取成功</div>' +
                        '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px;">' +
                        '<strong>下载链接：</strong><br><code style="word-break: break-all;">' + result + '</code><br>' +
                        '<button class="copy-btn" onclick="copyToClipboard(\\'' + result + '\\')">复制链接</button></div>';
                    showResult('视频链接', content);
                })
                .catch(error => {
                    hideLoading();
                    showError('网络请求失败: ' + error.message);
                });
        }
        
        function getVideoInfo() {
            console.log('getVideoInfo 被调用');
            const url = extractAndValidateUrl();
            if (!url) return;
            showLoading();
            fetch('?data&url=' + encodeURIComponent(url))
                .then(response => response.text())
                .then(result => {
                    hideLoading();
                    try {
                        const info = JSON.parse(result);
                        const content = '<div class="success-message">✅ 视频详细信息获取成功</div>' +
                            '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px;">' +
                            '<div><strong>视频ID：</strong> ' + (info.aweme_id || '未知') + '</div>' +
                            '<div><strong>标题：</strong> ' + (info.desc || '无标题') + '</div>' +
                            '<div><strong>作者：</strong> ' + (info.nickname || '未知') + '</div>' +
                            '<div><strong>点赞数：</strong> ' + ((info.digg_count && info.digg_count.toLocaleString()) || '0') + '</div>' +
                            '<div><strong>评论数：</strong> ' + ((info.comment_count && info.comment_count.toLocaleString()) || '0') + '</div>' +
                            '<div><strong>下载链接：</strong><br><code style="word-break: break-all;">' + (info.video_url || '获取失败') + '</code>' +
                            (info.video_url ? '<br><button class="copy-btn" onclick="copyToClipboard(\\'' + info.video_url + '\\')">复制链接</button>' : '') +
                            '</div></div>';
                        showResult('视频详细信息', content);
                    } catch (e) {
                        showError('响应格式错误: ' + result);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('解析失败: ' + error.message);
                });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const urlInput = document.getElementById('videoUrl');
            if (urlInput) {
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') getVideoUrl();
                });
                urlInput.focus();
            }
        });
    </script>
</body>
</html>`;
}

const handler = async (req: Request) => {
  console.log("Method:", req.method);
  const url = new URL(req.url);
  
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type",
  };
  
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }
  
  if (url.searchParams.has("url")) {
    const inputUrl = url.searchParams.get("url")!;
    console.log("inputUrl:", inputUrl);
    
    try {
      if (url.searchParams.has("data")) {
        const videoInfo = await getVideoInfo(inputUrl);
        return new Response(JSON.stringify(videoInfo), {
          headers: { "Content-Type": "application/json", ...corsHeaders }
        });
      }
      
      const videoUrl = await getVideoUrl(inputUrl);
      return new Response(videoUrl, { headers: corsHeaders });
    } catch (error) {
      return new Response(`错误: ${error.message}`, { 
        status: 500, headers: corsHeaders 
      });
    }
  } else {
    return new Response(generateHTML(), {
      headers: { "Content-Type": "text/html; charset=utf-8", ...corsHeaders }
    });
  }
};

Deno.serve({ port: 8080 }, handler);
