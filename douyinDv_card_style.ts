// 抖音视频下载服务 - 卡片式布局版本
const pattern = /"video":{"play_addr":{"uri":"([a-z0-9]+)"/;
const cVUrl = "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=1080p&line=0";
const statsRegex = /"statistics"\s*:\s*\{([\s\S]*?)\},/;
const regex = /"nickname":\s*"([^"]+)",\s*"signature":\s*"([^"]+)"/;
const ctRegex = /"create_time":\s*(\d+)/;
const descRegex = /"desc":\s*"([^"]+)"/;

interface DouyinVideoInfo {
  aweme_id: string | null;
  comment_count: number | null;
  digg_count: number | null;
  share_count: number | null;
  collect_count: number | null;
  nickname: string | null;
  signature: string | null;
  desc: string | null;
  create_time: string | null;
  video_url: string | null;
  type: string | null;
  image_url_list: string[] | null;
}

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

async function doGet(url: string): Promise<Response> {
  const headers = new Headers();
  headers.set(
    "User-Agent",
    "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36",
  );
  const resp = await fetch(url, { method: "GET", headers });
  return resp;
}

async function getVideoInfo(url: string): Promise<DouyinVideoInfo> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  
  const video_url = cVUrl.replace("%s", match[1]);
  const auMatch = body.match(regex);
  const ctMatch = body.match(ctRegex);
  const descMatch = body.match(descRegex);
  const statsMatch = body.match(statsRegex);
  
  if (statsMatch) {
    const innerContent = statsMatch[0];
    const awemeIdMatch = innerContent.match(/"aweme_id"\s*:\s*"([^"]+)"/);
    const commentCountMatch = innerContent.match(/"comment_count"\s*:\s*(\d+)/);
    const diggCountMatch = innerContent.match(/"digg_count"\s*:\s*(\d+)/);
    const shareCountMatch = innerContent.match(/"share_count"\s*:\s*(\d+)/);
    const collectCountMatch = innerContent.match(/"collect_count"\s*:\s*(\d+)/);
    
    const douyinVideoInfo: DouyinVideoInfo = {
      aweme_id: awemeIdMatch ? awemeIdMatch[1] : null,
      comment_count: commentCountMatch ? parseInt(commentCountMatch[1]) : null,
      digg_count: diggCountMatch ? parseInt(diggCountMatch[1]) : null,
      share_count: shareCountMatch ? parseInt(shareCountMatch[1]) : null,
      collect_count: collectCountMatch ? parseInt(collectCountMatch[1]) : null,
      nickname: null,
      signature: null,
      desc: null,
      create_time: null,
      video_url: video_url,
      type: "video",
      image_url_list: null
    };
    
    if (auMatch) {
      douyinVideoInfo.nickname = auMatch[1];
      douyinVideoInfo.signature = auMatch[2];
    }
    if (ctMatch) {
      const date = new Date(parseInt(ctMatch[1]) * 1000);
      douyinVideoInfo.create_time = formatDate(date);
    }
    if (descMatch) {
      douyinVideoInfo.desc = descMatch[1];
    }
    
    return douyinVideoInfo;
  } else {
    throw new Error("No stats found in the response.");
  }
}

async function getVideoUrl(url: string): Promise<string> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  return cVUrl.replace("%s", match[1]);
}

// 生成卡片式布局的HTML页面
function generateHTML(): string {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频下载工具 - 卡片式布局</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- 自定义样式 -->
    <style>
        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 毛玻璃效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* 加载动画 */
        .spinner {
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 按钮渐变效果 */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #e081e9 0%, #e3455a 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
        }

        /* Toast通知样式 */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            font-size: 14px;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.toast-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast.toast-error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        }

        /* 信息卡片配色 */
        .info-card-basic {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.08) 0%, rgba(155, 89, 182, 0.08) 100%);
        }

        .info-card-author {
            background: linear-gradient(135deg, rgba(230, 126, 34, 0.08) 0%, rgba(231, 76, 60, 0.08) 100%);
        }

        .info-card-stats {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.08) 0%, rgba(26, 188, 156, 0.08) 100%);
        }

        /* 复制按钮样式 */
        .copy-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1ba085 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-5 font-sans">
    <div class="max-w-6xl mx-auto">
        <!-- 页面标题区域 -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold text-black mb-3 tracking-tight">🎵 抖音视频下载工具</h1>
            <p class="text-white text-xl mb-2 drop-shadow-lg">快速获取无水印视频链接和详细信息</p>

        <!-- 主要内容区域 -->
        <div class="glass-effect rounded-3xl shadow-2xl p-8 mb-6 border border-white/20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-5 mb-6">
                <!-- 输入功能卡片 -->
                <div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover">
                    <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">
                        <span class="text-xl">📝</span>
                        <span>视频链接输入</span>
                    </div>
                    <div class="mb-5">
                        <textarea
                            id="videoUrl"
                            class="w-full p-5 border-2 border-white/30 rounded-xl text-base transition-all duration-300 glass-card resize-none font-inherit leading-relaxed min-h-[120px] focus:outline-none focus:border-blue-500 focus:bg-white/95 focus:shadow-lg focus:ring-4 focus:ring-blue-500/15 focus:-translate-y-0.5"
                            placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                            rows="3"
                        ></textarea>
                    </div>
                    <div class="flex gap-3 flex-wrap">
                        <button class="flex-1 min-w-[140px] px-5 py-3 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 flex items-center justify-center gap-2 btn-primary text-white shadow-lg" onclick="getVideoUrl()">
                            获取视频链接
                        </button>
                        <button class="flex-1 min-w-[140px] px-5 py-3 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 flex items-center justify-center gap-2 btn-secondary text-white shadow-lg" onclick="getVideoInfo()">
                            获取详细信息
                        </button>
                    </div>
                </div>

                <!-- 使用说明卡片 -->
                <div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover">
                    <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">
                        <span class="text-xl">📖</span>
                        <span>使用说明</span>
                    </div>
                    <ul class="list-none p-0 text-left">
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            支持抖音APP分享的短链接（如：https://v.douyin.com/xxxx/）
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            可以直接粘贴抖音分享的完整文本，系统会自动提取链接
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            "获取视频链接"返回可直接下载的无水印视频地址
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            "获取详细信息"返回视频的完整元数据信息
                        </li>
                        <li class="mb-0 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            点击复制按钮可快速复制结果到剪贴板
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 加载状态卡片 -->
            <div id="loadingCard" class="glass-card rounded-2xl shadow-lg border-2 border-blue-500/30 p-6 text-center hidden mb-5">
                <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 justify-center">
                    <span class="text-xl">⏳</span>
                    <span>正在处理</span>
                </div>
                <div class="spinner"></div>
                <p class="text-gray-600">正在解析视频信息，请稍候...</p>
            </div>

            <!-- 结果展示区域 -->
            <div id="resultsGrid" class="grid grid-cols-1 lg:grid-cols-3 gap-5 mt-6 hidden">
                <!-- 结果卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局函数定义，供onclick使用

        // 提取URL的正则表达式
        const urlRegex = /https?:\\/\\/v\\.douyin\\.com\\/[A-Za-z0-9]+\\/?/;

        // 显示加载状态
        function showLoading() {
            $('#loadingCard').removeClass('hidden').addClass('block');
            $('#resultsGrid').removeClass('grid').addClass('hidden');
            $('button').prop('disabled', true);
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#loadingCard').removeClass('block').addClass('hidden');
            $('button').prop('disabled', false);
        }

        // 显示Toast通知
        function showToast(message, type = 'success') {
            const toast = $('<div class="toast toast-' + type + '"></div>');
            const toastContent = $('<div class="flex items-center gap-2"></div>');
            const toastIcon = $('<span></span>').text(type === 'success' ? '✅' : '❌');
            const toastMessage = $('<span></span>').text(message);

            toastContent.append(toastIcon).append(toastMessage);
            toast.append(toastContent);
            $('body').append(toast);

            setTimeout(function() {
                toast.addClass('show');
            }, 100);

            setTimeout(function() {
                toast.removeClass('show');
                setTimeout(function() {
                    toast.remove();
                }, 300);
            }, 3000);
        }

        // 复制到剪贴板
        function copyToClipboard(text, buttonElement) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    updateCopyButton(buttonElement);
                    showToast('链接已复制到剪贴板！');
                }).catch(function(err) {
                    showToast('复制失败，请手动复制', 'error');
                });
            } else {
                // 降级方案
                const textArea = $('<textarea>').val(text).appendTo('body');
                textArea[0].select();
                document.execCommand('copy');
                textArea.remove();
                updateCopyButton(buttonElement);
                showToast('链接已复制到剪贴板！');
            }
        }

        // 更新复制按钮状态
        function updateCopyButton(buttonElement) {
            const $btn = $(buttonElement);
            const originalText = $btn.text();
            const originalClass = $btn.attr('class');

            $btn.text('✅ 已复制').removeClass().addClass('px-2 py-1 bg-green-500 text-white text-xs rounded cursor-pointer ml-2 transition-all duration-300');

            setTimeout(function() {
                $btn.text(originalText).removeClass().addClass(originalClass);
            }, 2000);
        }

        // 提取并验证URL
        function extractAndValidateUrl() {
            const input = $('#videoUrl').val().trim();
            if (!input) {
                showToast('请输入抖音视频链接', 'error');
                return null;
            }

            const match = input.match(urlRegex);
            if (match) {
                return match[0];
            }

            if (input.startsWith('http')) {
                return input;
            }

            showToast('未找到有效的抖音视频链接，请检查输入格式', 'error');
            return null;
        }

        // 显示结果卡片
        function showResults(cards) {
            const $resultsGrid = $('#resultsGrid');
            $resultsGrid.empty();

            cards.forEach(function(cardData) {
                const $card = $('<div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover ' + (cardData.cardClass || '') + '"></div>');
                const $cardTitle = $('<div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200"></div>');
                $cardTitle.html('<span class="text-xl">' + cardData.icon + '</span><span>' + cardData.title + '</span>');

                const $cardContent = $('<div></div>').html(cardData.content);

                $card.append($cardTitle).append($cardContent);
                $resultsGrid.append($card);
            });

            $resultsGrid.removeClass('hidden').addClass('grid');
        }

            // 获取视频链接
            function getVideoUrl() {
                const url = extractAndValidateUrl();
                if (!url) return;

                showLoading();

                fetch('?url=' + encodeURIComponent(url))
                    .then(function(response) {
                        return response.text();
                    })
                    .then(function(result) {
                        hideLoading();

                        const cards = [{
                            icon: '🎬',
                            title: '视频下载链接',
                            cardClass: 'info-card-basic',
                            content:
                                '<div class="flex items-start py-2 border-b border-gray-100 last:border-b-0">' +
                                    '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">下载地址：</span>' +
                                    '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' +
                                        '<code class="text-xs bg-gray-100 px-2 py-1 rounded border border-gray-200 break-all">' + result + '</code>' +
                                        '<button class="copy-btn text-white border-none px-4 py-2 rounded-lg cursor-pointer text-xs ml-2 transition-all duration-300 shadow-md" onclick="copyToClipboard(\\'' + result + '\\', this)">复制链接</button>' +
                                    '</span>' +
                                '</div>' +
                                '<div class="mt-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-700">' +
                                    '💡 提示：点击复制按钮复制链接，然后在浏览器中打开即可下载视频' +
                                '</div>'
                        }];

                        showResults(cards);
                    })
                    .catch(function(error) {
                        hideLoading();
                        showToast('网络请求失败，请检查网络连接: ' + error.message, 'error');
                    });
            }

            // 获取视频详细信息
            function getVideoInfo() {
                const url = extractAndValidateUrl();
                if (!url) return;

                showLoading();

                fetch('?data&url=' + encodeURIComponent(url))
                    .then(function(response) {
                        return response.text();
                    })
                    .then(function(result) {
                        hideLoading();

                        try {
                            const videoInfo = JSON.parse(result);

                            const cards = [
                                {
                                    icon: '📋',
                                    title: '基本信息',
                                    cardClass: 'info-card-basic',
                                    content:
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">视频ID：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + (videoInfo.aweme_id || '未知') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">标题：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + (videoInfo.desc || '无标题') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">创建时间：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + (videoInfo.create_time || '未知') + '</span>' +
                                        '</div>'
                                },
                                {
                                    icon: '👤',
                                    title: '作者信息',
                                    cardClass: 'info-card-author',
                                    content:
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">作者：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + (videoInfo.nickname || '未知') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">签名：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + (videoInfo.signature || '无签名') + '</span>' +
                                        '</div>'
                                },
                                {
                                    icon: '📊',
                                    title: '互动数据',
                                    cardClass: 'info-card-stats',
                                    content:
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">点赞数：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + ((videoInfo.digg_count && videoInfo.digg_count.toLocaleString()) || '0') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">评论数：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + ((videoInfo.comment_count && videoInfo.comment_count.toLocaleString()) || '0') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2 border-b border-gray-100">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">分享数：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + ((videoInfo.share_count && videoInfo.share_count.toLocaleString()) || '0') + '</span>' +
                                        '</div>' +
                                        '<div class="flex items-start py-2">' +
                                            '<span class="font-medium text-gray-600 min-w-[90px] flex-shrink-0 text-sm leading-relaxed">收藏数：</span>' +
                                            '<span class="text-gray-800 break-all flex-1 ml-4 text-sm leading-relaxed">' + ((videoInfo.collect_count && videoInfo.collect_count.toLocaleString()) || '0') + '</span>' +
                                        '</div>'
                                }
                            ];

                            showResults(cards);
                        } catch (parseError) {
                            showToast('响应格式错误: ' + result, 'error');
                        }
                    })
                    .catch(function(error) {
                        hideLoading();
                        showToast('解析失败，请检查链接是否正确: ' + error.message, 'error');
                    });
        }

        // 页面初始化
        $(document).ready(function() {
            console.log('卡片式布局页面已加载');

            // 回车键支持
            $('#videoUrl').keypress(function(e) {
                if (e.which === 13) {
                    getVideoUrl();
                }
            });

            // 自动聚焦输入框
            $('#videoUrl').focus();
        });
    </script>
</body>
</html>
  `;
}

const handler = async (req: Request) => {
  console.log("Method:", req.method);
  const url = new URL(req.url);

  // 设置CORS头
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type",
  };

  // 处理OPTIONS请求
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  if (url.searchParams.has("url")) {
    const inputUrl = url.searchParams.get("url")!;
    console.log("inputUrl:", inputUrl);

    try {
      // 返回完整json数据
      if (url.searchParams.has("data")) {
        const videoInfo = await getVideoInfo(inputUrl);
        return new Response(JSON.stringify(videoInfo), {
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders
          }
        });
      }

      const videoUrl = await getVideoUrl(inputUrl);
      return new Response(videoUrl, {
        headers: corsHeaders
      });
    } catch (error) {
      return new Response(`错误: ${error.message}`, {
        status: 500,
        headers: corsHeaders
      });
    }
  } else {
    // 返回卡片式布局的HTML页面
    return new Response(generateHTML(), {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
        ...corsHeaders
      }
    });
  }
};

Deno.serve({ port: 8082 }, handler);
